import WebSocket from 'ws';
import { spawn } from 'child_process';
import axios from 'axios';
import path from 'path';
import { config } from 'dotenv';
import express from 'express';
import cors from 'cors';
import { createServer } from 'http';
import { QueueService } from './services/queue-service';
import { DashboardService } from './services/dashboard-service';
import { llmService } from './services/llm-service';

config();

interface TestExecutionRequest {
  type: string;
  token: string;
  authToken?: string;
  testCaseId: string;
  tcId: string;
  projectId: string;
  testRunId: string;
  steps: any[];
  testCase: {
    title: string;
    precondition: string;
    expectation: string;
    projectId: string;
  };
}



class TestRunnerWebSocketServer {
  private wss: WebSocket.Server;
  private port: number;
  private backendUrl: string;
  private app: express.Application;
  private server: any;
  private activeConnections: Map<string, WebSocket> = new Map();

  constructor(port: number = 3024) {
    this.port = port;
    this.backendUrl = process.env.BACKEND_URL || 'http://localhost:3010';

    // Create Express app for queue management
    this.app = express();
    this.app.use(cors());
    this.app.use(express.json());

    // Create HTTP server
    this.server = createServer(this.app);

    // Create WebSocket server attached to HTTP server
    this.wss = new WebSocket.Server({
      server: this.server,
      perMessageDeflate: false
    });

    // Setup emergency cleanup on process exit
    this.setupEmergencyCleanup();

    console.log(`TestRun WebSocket server running on port ${this.port}`);
    this.initializeServices();
    this.setupWebSocketHandlers();
    this.startServer();
  }

  private async initializeServices() {
    try {
      await QueueService.initialize();
      console.log('✅ Queue Service initialized (TestRun)');

      DashboardService.initialize(this.app as any);
      DashboardService.addStatsEndpoint(this.app as any);
      console.log('✅ Dashboard Service initialized (TestRun)');
    } catch (error) {
      console.warn('⚠️ Failed to initialize Queue/Dashboard services (Redis auth issue):', error instanceof Error ? error.message : error);
      console.log('📝 Server will continue without queue functionality - tests will run directly');
    }
  }

  private startServer() {
    this.server.listen(this.port, () => {
      console.log(`🎯 Bull Board Dashboard available at: http://localhost:${this.port}/admin/queues`);
      console.log(`📊 Queue Stats API available at: http://localhost:${this.port}/api/queue/stats`);

      // Add debug endpoint
      this.app.get('/debug/worker', async (_req, res) => {
        await QueueService.debugWorkerStatus();
        res.json({ message: 'Worker status logged to console' });
      });

      // Add cleanup endpoints that the backend expects
      this.setupCleanupEndpoints();
    }).on('error', (error: any) => {
      if (error.code === 'EADDRINUSE') {
        console.error(`Port ${this.port} is already in use`);
        process.exit(1);
      }
    });
  }

  private setupCleanupEndpoints() {
    // Import ZapService for cleanup operations
    const { ZapService } = require('./services/zap-service');

    // Health check endpoint
    this.app.get('/health', (_req, res) => {
      res.json({ status: 'healthy', timestamp: new Date().toISOString() });
    });

    // Cleanup specific client resources
    this.app.post('/api/cleanup/kubernetes', async (req, res) => {
      try {
        const { clientId } = req.body;

        if (!clientId) {
          return res.status(400).json({
            success: false,
            message: 'clientId is required'
          });
        }

        console.log(`🧹 HTTP cleanup request for CLIENT_ID: ${clientId}`);

        // Call the same cleanup method used by test completion
        await ZapService.removeClientContainer(clientId);

        res.json({
          success: true,
          message: `Kubernetes resources cleaned up for CLIENT_ID: ${clientId}`,
          clientId
        });
      } catch (error) {
        console.error('❌ HTTP cleanup failed:', error);
        res.status(500).json({
          success: false,
          message: `Cleanup failed: ${error instanceof Error ? error.message : String(error)}`
        });
      }
    });

    // Cleanup orphaned resources
    this.app.post('/api/cleanup/orphaned', async (req, res) => {
      try {
        const { olderThanMinutes = 10 } = req.body;

        console.log(`🧹 HTTP orphaned cleanup request (older than ${olderThanMinutes} minutes)`);

        // Import KubernetesService for orphaned cleanup
        const { KubernetesService } = require('./services/kubernetes-service');

        if (process.env.USE_KUBERNETES !== 'false') {
          await KubernetesService.cleanupOrphanedJobs(olderThanMinutes);
        } else {
          // For Docker mode, we'd need to implement orphaned container cleanup
          console.log('📝 Docker mode: orphaned cleanup not implemented');
        }

        res.json({
          success: true,
          message: `Orphaned resources cleanup completed (older than ${olderThanMinutes} minutes)`,
          olderThanMinutes
        });
      } catch (error) {
        console.error('❌ HTTP orphaned cleanup failed:', error);
        res.status(500).json({
          success: false,
          message: `Orphaned cleanup failed: ${error instanceof Error ? error.message : String(error)}`
        });
      }
    });

    // Get active ZAP jobs
    this.app.get('/api/zap/jobs', async (_req, res) => {
      try {
        // Get active jobs from ZapService
        const activeJobs = Array.from(ZapService.getActiveJobs().keys());

        res.json({
          success: true,
          jobs: activeJobs,
          message: 'Successfully retrieved active ZAP jobs'
        });
      } catch (error) {
        console.error('❌ Failed to get active ZAP jobs:', error);
        res.status(500).json({
          success: false,
          jobs: [],
          message: `Failed to retrieve jobs: ${error instanceof Error ? error.message : String(error)}`
        });
      }
    });

    console.log('✅ Cleanup endpoints configured');
  }

  private setupEmergencyCleanup() {
    // Handle process termination signals for emergency cleanup
    const cleanup = async () => {
      try {
        console.log('🚨 Emergency cleanup triggered...');
        const { ZapService } = require('./services/zap-service');

        // Get all active jobs and clean them up
        const activeJobs = Array.from(ZapService.getActiveJobs().keys());
        if (activeJobs.length > 0) {
          console.log(`🧹 Emergency cleanup: Found ${activeJobs.length} active ZAP jobs`);
          for (const clientId of activeJobs) {
            try {
              await ZapService.removeClientContainer(clientId);
              console.log(`✅ Emergency cleanup completed for CLIENT_ID: ${clientId}`);
            } catch (error) {
              console.error(`❌ Emergency cleanup failed for CLIENT_ID: ${clientId}:`, error);
            }
          }
        } else {
          console.log('📝 No active ZAP jobs found during emergency cleanup');
        }
      } catch (error) {
        console.error('❌ Emergency cleanup error:', error);
      }
    };

    // Handle various termination signals
    process.on('SIGINT', cleanup);
    process.on('SIGTERM', cleanup);
    process.on('SIGUSR2', cleanup); // nodemon restart signal
    process.on('exit', cleanup);

    console.log('✅ Emergency cleanup handlers registered');
  }

  private setupWebSocketHandlers() {

    const API_VALIDATE_URL = `${process.env.CORE_SERVICE_URL}/profile/validate-api-key`;
    const API_PROFILE_BY_KEY_URL = `${process.env.CORE_SERVICE_URL}/profile/profile-by-key`;

    this.wss.on('connection', (ws: WebSocket) => {
      console.log('Client connected to TestRun server');
      let isAuthenticated = false;

      ws.on('message', async (message: string) => {
        try {
          const data = JSON.parse(message);
          console.log('Received message type:', data.type);

          switch (data.type) {
            case 'authenticate':
            case 'auth': // Support both message types for compatibility
              try {
                const authToken = data.authToken || data.token;

                if (!authToken) {
                  throw new Error('No authentication token provided');
                }

                // Check if this looks like a JWT token (has dots) or AgentQ API key
                const isJwtToken = authToken.includes('.');

                if (isJwtToken) {
                  // JWT token authentication (for main test execution)
                  console.log('Validating JWT token:', authToken.substring(0, 8) + '...');

                  const response = await axios.get(`${this.backendUrl}/api-keys`, {
                    headers: {
                      'Authorization': `Bearer ${authToken}`
                    }
                  });

                  console.log('JWT validation response status:', response.status);

                  if (response.status === 200) {
                    isAuthenticated = true;
                    ws.send(JSON.stringify({
                      type: 'auth_success',
                      message: 'Authentication successful'
                    }));
                    console.log('Authentication successful for JWT token:', authToken.substring(0, 8) + '..., waiting for commands...');
                  } else {
                    throw new Error('Invalid JWT token');
                  }
                } else {
                  // AgentQ API key authentication (for q() function calls)
                  console.log('Validating AgentQ API key:', authToken.substring(0, 8) + '...');

                  // For AgentQ API key, we'll accept it if it looks valid (length check)
                  // In a full implementation, you'd validate against AgentQ service
                  if (authToken && authToken.length > 20) {
                    isAuthenticated = true;
                    ws.send(JSON.stringify({
                      type: 'auth_success',
                      message: 'Authentication successful'
                    }));
                    console.log('Authentication successful for AgentQ API key:', authToken.substring(0, 8) + '..., waiting for commands...');
                  } else {
                    throw new Error('Invalid AgentQ API key');
                  }
                }
              } catch (error: any) {
                console.log('Authentication failed:', error.response?.data?.message || error.message);
                ws.send(JSON.stringify({
                  type: 'auth_failed',
                  message: error.response?.data?.message || error.message || 'Authentication failed'
                }));
              }
              break;

            case 'execute_test':
              if (!isAuthenticated) {
                ws.send(JSON.stringify({
                  type: 'test_error',
                  message: 'Not authenticated'
                }));
                return;
              }

              console.log('Test data for:', data.testCase?.title);



              // Try to use queue system if available, otherwise execute directly
              try {
                const clientId = `client-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

                // Store WebSocket connection for this client
                this.activeConnections.set(clientId, ws);

                // Get API key from auth token (we'll need to fetch this)
                let apiKey = 'default-api-key'; // Fallback
                try {
                  const apiKeysResponse = await axios.get(`${this.backendUrl}/api-keys`, {
                    headers: {
                      'Authorization': `Bearer ${data.authToken}`
                    }
                  });
                  if (apiKeysResponse.data && apiKeysResponse.data.length > 0) {
                    apiKey = apiKeysResponse.data[0].apiKey;
                  }
                } catch (apiError) {
                  console.warn('Failed to fetch API key for queue, using fallback');
                }

                // Format test data properly for queue processing (same as direct execution)
                const { testCaseId, tcId, projectId, testRunId, steps, testCase, authToken } = data;
                const formattedTestData = {
                  testCaseId,
                  tcId: tcId.toString(),
                  testCase: {
                    id: testCaseId,
                    tcId: tcId.toString(),
                    title: testCase.title,
                    precondition: testCase.precondition,
                    expectation: testCase.expectation
                  },
                  projectId,
                  testRunId,
                  steps: steps || [],
                  authToken: authToken || null
                };



                // Add job to queue with properly formatted data
                const { jobId, position } = await QueueService.addTestJob(apiKey, formattedTestData, clientId);

                console.log(`✅ Test job ${jobId} added to queue for client: ${clientId} at position: ${position}`);

                // Get queue stats immediately after adding
                const queueStats = await QueueService.getQueueStats();

                ws.send(JSON.stringify({
                  type: 'test_queued',
                  message: `Test added to queue at position ${position}`,
                  clientId: clientId,
                  jobId: jobId,
                  position: position,
                  queueStats: queueStats
                }));

              } catch (queueError) {
                console.warn('⚠️ Queue system not available, executing test directly:', queueError);
                await this.executeTest(ws, data);
              }
              break;

            case 'command': {
              if (!data.token) {
                ws.send(JSON.stringify({ type: 'error', message: 'API key is required for commands.' }));
                return;
              }

              try {
                // Validate API Key first
                const validateResponse = await fetch(API_VALIDATE_URL, {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                    'Accept': '*/*'
                  },
                  body: JSON.stringify({ apiKey: data.token }),
                });

                if (!validateResponse.ok) {
                  console.error('API Key validation failed:', validateResponse.status, validateResponse.statusText);
                  ws.send(JSON.stringify({
                    type: 'error',
                    message: 'Invalid API key.'
                  }));
                  return;
                }

                const validationResult = await validateResponse.json() as { valid: boolean };

                if (!validationResult.valid) {
                  ws.send(JSON.stringify({
                    type: 'error',
                    message: 'Invalid API key.'
                  }));
                  return;
                }

                // Fetch user profile to check token usage
                const profileResponse = await fetch(API_PROFILE_BY_KEY_URL, {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                    'Accept': '*/*'
                  },
                  body: JSON.stringify({ apiKey: data.token }),
                });

                if (!profileResponse.ok) {
                  console.error('Failed to fetch profile:', profileResponse.status, profileResponse.statusText);
                  ws.send(JSON.stringify({
                    type: 'error',
                    message: 'Failed to fetch user profile.'
                  }));
                  return;
                }

                const profileData = await profileResponse.json() as { company?: { subscription?: { remainingTokens: number } } };

                if (profileData?.company?.subscription?.remainingTokens === 0) {
                  ws.send(JSON.stringify({
                    type: 'error',
                    message: 'Your remaining token is 0. Please check your token usage on https://agentq.id.'
                  }));
                  return;
                }

                if (!data.prompt || !data.pageSource) {
                  ws.send(JSON.stringify({
                    type: 'error',
                    message: 'Prompt and pageSource are required for the command.'
                  }));
                  return;
                }

                // Process the command through the LLM service with apiKey for token tracking
                const command = await llmService.getCommandFromAI(data.prompt, data.pageSource, data.token);

                if (!command) {
                  ws.send(JSON.stringify({
                    type: 'error',
                    message: 'Failed to generate command from AI'
                  }));
                  return;
                }

                ws.send(JSON.stringify({ type: 'response', command }));
                break;
              } catch (error) {
                console.error('Error processing command:', error);
                ws.send(JSON.stringify({
                  type: 'error',
                  message: 'Failed to process command. Please try again.'
                }));
              }
            }
            default:
              ws.send(JSON.stringify({ type: 'error', message: 'Unknown message type.' }));
          }
        } catch (error) {
          console.error('Error processing message:', error);
          ws.send(JSON.stringify({
            type: 'error',
            message: 'Failed to process command. Please try again.'
          }));
        }
      });

      ws.on('close', () => {
        console.log('Client disconnected from TestRun server');
        // Clean up active connections
        for (const [clientId, connection] of this.activeConnections.entries()) {
          if (connection === ws) {
            this.activeConnections.delete(clientId);
            console.log(`Cleaned up connection for client: ${clientId}`);
            break;
          }
        }
      });

      ws.on('error', (error) => {
        console.error('WebSocket error:', error);
      });
    });
  }

  private async executeTest(ws: WebSocket, testData: TestExecutionRequest) {
    const { testCaseId, tcId, projectId, testRunId, steps, testCase, authToken } = testData;

    try {
      // Fetch AgentQ API key from backend using JWT token
      let agentqApiKey = '';
      try {
        const apiKeysResponse = await axios.get(`${this.backendUrl}/api-keys`, {
          headers: {
            'Authorization': `Bearer ${authToken}`
          }
        });

        if (apiKeysResponse.data && apiKeysResponse.data.length > 0) {
          agentqApiKey = apiKeysResponse.data[0].apiKey;
          console.log('Retrieved AgentQ API key for test execution');
        }
      } catch (error) {
        console.warn('Failed to fetch AgentQ API key, test may fail:', error);
      }

      // Create test data for master.spec.ts format
      const testDataContent = {
        testCase: {
          id: testCaseId,
          tcId: tcId.toString(),
          title: testCase.title,
          precondition: testCase.precondition,
          expectation: testCase.expectation
        },
        projectId,
        testRunId,
        steps: steps || [],
        authToken: authToken || null
      };

      // Use testrun-detail.spec.ts which has backend integration for TestRunDetail
      const testFilePath = path.join(__dirname, '../tests/testrun-detail.spec.ts');
      console.log('Executing: test "' + testFilePath + '" --reporter=line');

      // Execute the test
      const testProcess = spawn('npx', [
        'playwright',
        'test',
        testFilePath,
        '--reporter=line'
      ], {
        cwd: path.join(__dirname, '..'), // Run from the root directory
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
          ...process.env,
          TEST_DATA: JSON.stringify(testDataContent), // Pass test data as JSON string
          BACKEND_URL: this.backendUrl,
          PROJECT_ID: projectId,
          TEST_RUN_ID: testRunId,
          TEST_CASE_ID: testCaseId,
          AUTH_TOKEN: authToken || '',
          AGENTQ_TOKEN: agentqApiKey, // AgentQ library looks for this variable
          AGENTQ_API_KEY: agentqApiKey, // Alternative variable name
          AGENTQ_JWT_TOKEN: authToken, // Pass JWT token for AgentQ library authentication
          AGENTQ_SERVICE_URL: `${process.env.VITE_WEBSOCKET_URL}`, // Point to our current WebSocket server
          AGENTQ_ENV: 'development', // Force development mode
          NODE_ENV: 'development' // Ensure development environment
        }
      });

      ws.send(JSON.stringify({
        type: 'test_start',
        message: 'Test execution started'
      }));

      console.log('Connection stabilized');

      // Handle test output
      testProcess.stdout.on('data', (data) => {
        const output = data.toString();
        console.log('Test output:', output);
        ws.send(JSON.stringify({
          type: 'test_output',
          output: output
        }));
      });

      testProcess.stderr.on('data', (data) => {
        const error = data.toString();
        console.log('Test error:', error);
        ws.send(JSON.stringify({
          type: 'test_output',
          output: error
        }));
      });

      // Handle test completion
      testProcess.on('close', async (code) => {
        console.log('Test process exited with code:', code);

        const status = code === 0 ? 'passed' : 'failed';

        ws.send(JSON.stringify({
          type: 'test_complete',
          status: status,
          message: `Test ${status} with exit code ${code}`
        }));

        // Upload video and screenshot if test completed (regardless of pass/fail)
        console.log('🔄 Starting upload process...');
        console.log('📊 Upload context:', {
          hasAuthToken: !!authToken,
          authTokenLength: authToken?.length || 0,
          projectId: testData.projectId,
          testRunId: testData.testRunId,
          testCaseId: testData.testCaseId,
          tcId: testData.tcId
        });

        if (authToken) {
          console.log('🎥 Attempting video upload...');
          try {
            await this.uploadTestVideo(testData, authToken);
            console.log('✅ Video upload completed successfully');
          } catch (videoError: any) {
            console.error('❌ Failed to upload test video:', videoError);
            if (videoError.response) {
              console.error('❌ Video upload response status:', videoError.response.status);
              console.error('❌ Video upload response data:', videoError.response.data);
            }
          }

          console.log('📸 Attempting screenshot upload...');
          try {
            console.log('📸 About to call uploadTestScreenshot method...');
            await this.uploadTestScreenshot(testData, authToken);
            console.log('✅ Screenshot upload completed successfully');
          } catch (screenshotError: any) {
            console.error('❌ Failed to upload test screenshot:', screenshotError);
            console.error('❌ Screenshot error details:', screenshotError.message);
            console.error('❌ Screenshot error stack:', screenshotError.stack);
            if (screenshotError.response) {
              console.error('❌ Screenshot upload response status:', screenshotError.response.status);
              console.error('❌ Screenshot upload response data:', screenshotError.response.data);
            }
          }
        } else {
          console.log('❌ No auth token available for video/screenshot upload');
        }

        console.log('🏁 Upload process completed');

        // Note: No ZAP cleanup needed here as this test runner doesn't create ZAP containers
        // ZAP containers are created and cleaned up by the DAST test runner (test-runner.ts)
      });

      testProcess.on('error', (error) => {
        console.error('Test process error:', error);
        ws.send(JSON.stringify({
          type: 'test_error',
          message: error.message
        }));
      });

    } catch (error: any) {
      console.error('Error executing test:', error);
      ws.send(JSON.stringify({
        type: 'test_error',
        message: error.message || 'Test execution failed'
      }));
    }
  }

  // Upload test video to backend
  private async uploadTestVideo(testData: TestExecutionRequest, authToken: string): Promise<void> {
    let testResultId = 'unknown';

    try {
      const fs = require('fs');
      const path = require('path');
      const FormData = require('form-data');

      // Look for video files in test-results directory
      const testResultsDir = path.join(process.cwd(), 'test-results');
      console.log('🎥 Looking for test results in:', testResultsDir);

      if (!fs.existsSync(testResultsDir)) {
        console.log('❌ No test-results directory found, skipping video upload');
        return;
      }

      // Find video files - look for patterns like {projectId}/{testRunId}/{tcId}/video.webm
      const projectId = testData.projectId || 'unknown-project';
      const testRunId = testData.testRunId || 'unknown-testrun';
      const tcId = testData.tcId || 'unknown-tc';

      // Try multiple possible video paths
      const possibleVideoPaths = [
        path.join(testResultsDir, projectId, testRunId, tcId.toString(), 'video.webm'),
        path.join(testResultsDir, projectId, testRunId, testData.testCaseId, 'video.webm'),
        path.join(testResultsDir, `testrun-detail-${tcId}`, 'video.webm') // Fallback to old format
      ];

      // Also search for any video.webm files in subdirectories
      const findVideoFiles = (dir: string): string[] => {
        const videoFiles: string[] = [];
        try {
          const items = fs.readdirSync(dir);
          for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            if (stat.isDirectory()) {
              videoFiles.push(...findVideoFiles(fullPath));
            } else if (item === 'video.webm') {
              videoFiles.push(fullPath);
            }
          }
        } catch (error) {
          console.error('Error reading directory:', error);
        }
        return videoFiles;
      };

      // Get all video files
      const allVideoFiles = findVideoFiles(testResultsDir);
      console.log('🎥 All video files found:', allVideoFiles);

      // Add the specific paths we're looking for
      const videoFiles = [...possibleVideoPaths.filter(p => fs.existsSync(p)), ...allVideoFiles];
      console.log('🎥 Combined video files list:', videoFiles);

      if (videoFiles.length === 0) {
        console.log('❌ No video files found for upload');
        console.log('🎥 Checked paths:', possibleVideoPaths);
        console.log('🎥 Test results directory contents:', fs.readdirSync(testResultsDir));
        return;
      }

      // Use the most recent video file
      const videoFile = videoFiles.sort((a, b) => {
        const statA = fs.statSync(a);
        const statB = fs.statSync(b);
        return statB.mtime.getTime() - statA.mtime.getTime();
      })[0];

      console.log(`✅ Found video file for upload: ${videoFile}`);
      const videoStat = fs.statSync(videoFile);
      console.log(`🎥 Video file size: ${(videoStat.size / 1024 / 1024).toFixed(2)} MB`);
      console.log(`🎥 Video file modified: ${videoStat.mtime.toISOString()}`);

      // Wait a moment for the database transaction to complete
      console.log('🎥 Waiting for database transaction to complete...');
      await new Promise(resolve => setTimeout(resolve, 3000)); // 3 second delay

      // Get user profile from the provided auth token to get real user data
      let userProfile = null;
      let serviceToken = authToken; // Use the provided token first

      try {
        console.log('🎥 Fetching user profile to get user data...');
        const profileResponse = await axios.get(
          `${this.backendUrl}/auth/profile`,
          {
            headers: {
              'Authorization': `Bearer ${authToken}`
            },
            timeout: 5000
          }
        );
        userProfile = profileResponse.data;
        console.log('🎥 User profile fetched:', {
          id: userProfile.id,
          email: userProfile.email,
          name: userProfile.name
        });
      } catch (profileError: any) {
        console.warn('🎥 Failed to fetch user profile, falling back to service token:', profileError.message);

        // Fallback: Generate a service JWT token using the user's actual data if available, or minimal data
        const jwt = require('jsonwebtoken');
        const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret_key_change_in_production';

        // Use actual user data if we have it, otherwise use minimal service data
        const fallbackPayload = userProfile ? {
          email: userProfile.email,
          sub: userProfile.id,
          role: userProfile.role || 'user',
          name: userProfile.name,
          external_auth: true,
          companyId: userProfile.companyId || null
        } : {
          // Minimal service token if no user data available
          email: '<EMAIL>',
          sub: 'service-user',
          role: 'service',
          name: 'Service User',
          external_auth: true,
          companyId: null
        };

        serviceToken = jwt.sign(fallbackPayload, JWT_SECRET, { expiresIn: '1h' });
        console.log('🎥 Generated fallback service token for:', fallbackPayload.email);
      }

      let testResultResponse;
      try {
        testResultResponse = await axios.get(
          `${this.backendUrl}/projects/${testData.projectId}/test-runs/${testData.testRunId}/test-results`,
          {
            headers: {
              'Authorization': `Bearer ${serviceToken}`
            },
            timeout: 5000,
            params: {
              limit: 1,
              sortField: 'createdAt',
              sortDirection: 'DESC'
            }
          }
        );
        console.log(`🎥 Test results response status:`, testResultResponse.status);
        console.log(`🎥 Test results response:`, testResultResponse.data);
      } catch (apiError: any) {
        console.error('🎥 Failed to fetch test results for video upload:', apiError);
        if (axios.isAxiosError(apiError) && apiError.response) {
          console.error('🎥 API response status:', apiError.response.status);
          console.error('🎥 API response data:', apiError.response.data);
        }
        return;
      }

      if (!testResultResponse.data || !testResultResponse.data.results || testResultResponse.data.results.length === 0) {
        console.log('🎥 No test result found for video upload');
        return;
      }

      // Get the most recent test result
      const testResult = testResultResponse.data.results[0];
      testResultId = testResult.id;
      console.log(`🎥 Uploading video for test result ID: ${testResultId}`);

      // Read the video file
      const videoBuffer = fs.readFileSync(videoFile);

      // Create form data for video upload
      const formData = new FormData();
      formData.append('file', videoBuffer, {
        filename: 'video.webm',
        contentType: 'video/webm'
      });

      // Upload video to backend using proper test-runs endpoint
      await axios.post(
        `${this.backendUrl}/projects/${testData.projectId}/test-runs/${testData.testRunId}/test-results/${testResultId}/video`,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
            'Authorization': `Bearer ${serviceToken}`
          },
          timeout: 30000, // 30 second timeout for video upload
          maxContentLength: 100 * 1024 * 1024, // 100MB max
          maxBodyLength: 100 * 1024 * 1024
        }
      );

      console.log(`🎥 Video uploaded successfully for test result: ${testResultId}`);

      

    } catch (error) {
      console.error('🎥 Failed to upload test video:', error);

      // Log more detailed error information
      if (axios.isAxiosError(error) && error.response) {
        console.error('🎥 Video upload response status:', error.response.status);
        console.error('🎥 Video upload response data:', error.response.data);
        console.error('🎥 Video upload URL was:', `${this.backendUrl}/projects/${testData.projectId}/test-runs/${testData.testRunId}/test-results/${testResultId}/video`);

        // If it's a 500 error on the test results fetch, it might be an endpoint issue
        if (error.config?.url?.includes('/test-results') && !error.config?.url?.includes('/video')) {
          console.error('🎥 ❌ Failed to fetch test results - the backend endpoint might not exist');
          console.error('🎥 ❌ This means the video upload endpoint for formal test results is not implemented in the backend');
        }
      } else if (axios.isAxiosError(error) && error.request) {
        console.error('🎥 No response received for video upload');
        console.error('🎥 Video upload URL was:', `${this.backendUrl}/projects/${testData.projectId}/test-runs/${testData.testRunId}/test-results/${testResultId}/video`);
      } else {
        console.error('🎥 Video upload error:', (error as Error).message);
      }
    }
  }

  // Upload test screenshot to backend
  private async uploadTestScreenshot(testData: TestExecutionRequest, authToken: string): Promise<void> {
    console.log('📸 === SCREENSHOT UPLOAD STARTED ===');
    console.log('📸 Upload parameters:', {
      projectId: testData.projectId,
      testRunId: testData.testRunId,
      testCaseId: testData.testCaseId,
      tcId: testData.tcId,
      authTokenPresent: !!authToken
    });

    let testResultId = 'unknown';
    try {
      const fs = require('fs');
      const path = require('path');
      const FormData = require('form-data');

      // Look for screenshot files in test-results directory
      const testResultsDir = path.join(process.cwd(), 'test-results');
      console.log('📸 Looking for test results in:', testResultsDir);

      if (!fs.existsSync(testResultsDir)) {
        console.log('📸 No test-results directory found, skipping screenshot upload');
        return;
      }

      console.log('📸 Test results directory exists, proceeding with screenshot detection...');

      // Look for Playwright's default screenshot pattern first (most common case)
      const glob = require('glob');
      let screenshotPath = '';

      // Try multiple screenshot patterns
      const screenshotPatterns = [
        // Look for screenshots in the same directory as the video (most likely location)
        path.join(testResultsDir, 'master-*', '*.png'),
        path.join(testResultsDir, 'testrun-detail-*', '*.png'),
        // Specific patterns for test completion screenshots
        path.join(testResultsDir, 'master-*', 'test-finished-*.png'),
        path.join(testResultsDir, 'testrun-detail-*', 'test-finished-*.png'),
        // Specific patterns for failed tests
        path.join(testResultsDir, 'master-*', 'test-failed-*.png'),
        path.join(testResultsDir, 'testrun-detail-*', 'test-failed-*.png'),
        // Generic screenshot patterns
        path.join(testResultsDir, '*', 'screenshot.png'),
        path.join(testResultsDir, '*', '*.png'),
        // Look for any PNG files in test results
        path.join(testResultsDir, '*.png')
      ];



      for (const pattern of screenshotPatterns) {
        const screenshots = glob.sync(pattern);
        if (screenshots.length > 0) {
          // Get the most recent screenshot
          screenshotPath = screenshots.sort((a: string, b: string) => {
            const statA = fs.statSync(a);
            const statB = fs.statSync(b);
            return statB.mtime.getTime() - statA.mtime.getTime();
          })[0];
          break;
        }
      }

      if (!screenshotPath || !fs.existsSync(screenshotPath)) {
        return;
      }

      // Wait a moment for the database transaction to complete
      await new Promise(resolve => setTimeout(resolve, 3000)); // 3 second delay

      // Get axios for API calls
      const axios = require('axios');

      // Get user profile from the provided auth token to get real user data
      let userProfile = null;
      let serviceToken = authToken; // Use the provided token first

      try {
        console.log('📸 Fetching user profile to get user data...');
        const profileResponse = await axios.get(
          `${this.backendUrl}/auth/profile`,
          {
            headers: {
              'Authorization': `Bearer ${authToken}`
            },
            timeout: 5000
          }
        );
        userProfile = profileResponse.data;
        console.log('📸 User profile fetched:', {
          id: userProfile.id,
          email: userProfile.email,
          name: userProfile.name
        });
      } catch (profileError: any) {
        console.warn('📸 Failed to fetch user profile, falling back to service token:', profileError.message);

        // Fallback: Generate a service JWT token using the user's actual data if available, or minimal data
        const jwt = require('jsonwebtoken');
        const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret_key_change_in_production';

        // Use actual user data if we have it, otherwise use minimal service data
        const fallbackPayload = userProfile ? {
          email: userProfile.email,
          sub: userProfile.id,
          role: userProfile.role || 'user',
          name: userProfile.name,
          external_auth: true,
          companyId: userProfile.companyId || null
        } : {
          // Minimal service token if no user data available
          email: '<EMAIL>',
          sub: 'service-user',
          role: 'service',
          name: 'Service User',
          external_auth: true,
          companyId: null
        };

        serviceToken = jwt.sign(fallbackPayload, JWT_SECRET, { expiresIn: '1h' });
        console.log('📸 Generated fallback service token for:', fallbackPayload.email);
      }

      let testResultResponse;
      try {
        testResultResponse = await axios.get(
          `${this.backendUrl}/projects/${testData.projectId}/test-runs/${testData.testRunId}/test-results`,
          {
            headers: {
              'Authorization': `Bearer ${serviceToken}`
            },
            timeout: 5000,
            params: {
              limit: 1,
              sortField: 'createdAt',
              sortDirection: 'DESC'
            }
          }
        );
        console.log(`📸 Test results response status:`, testResultResponse.status);
        console.log(`📸 Test results response:`, testResultResponse.data);
      } catch (apiError: any) {
        console.error('📸 Failed to fetch test results for screenshot upload:', apiError);
        if (axios.isAxiosError(apiError) && apiError.response) {
          console.error('📸 API response status:', apiError.response.status);
          console.error('📸 API response data:', apiError.response.data);
        }
        return;
      }


      if (!testResultResponse.data || !testResultResponse.data.results || testResultResponse.data.results.length === 0) {
        console.log('📸 No test result found for screenshot upload');
        return;
      }

      // Get the most recent test result
      const testResult = testResultResponse.data.results[0];
      testResultId = testResult.id;
      console.log(`📸 Uploading screenshot for test result ID: ${testResultId}`);

      // Read the screenshot file
      const screenshotBuffer = fs.readFileSync(screenshotPath);

      // Create form data for screenshot upload
      const formData = new FormData();
      formData.append('file', screenshotBuffer, {
        filename: 'screenshot.png',
        contentType: 'image/png'
      });

      // Upload screenshot to backend using proper test-runs endpoint
      await axios.post(
        `${this.backendUrl}/projects/${testData.projectId}/test-runs/${testData.testRunId}/test-results/${testResultId}/screenshot`,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
            'Authorization': `Bearer ${serviceToken}`
          },
          timeout: 30000, // 30 second timeout for screenshot upload
          maxContentLength: 100 * 1024 * 1024, // 100MB max
          maxBodyLength: 100 * 1024 * 1024
        }
      );

      console.log('📸 Screenshot uploaded successfully');

    } catch (error: any) {
      if (axios.isAxiosError(error) && error.response) {
        console.error('📸 Screenshot upload failed with status:', error.response.status);
        console.error('📸 Screenshot upload error response:', error.response.data);
      } else if (axios.isAxiosError(error) && error.request) {
        console.error('📸 No response received for screenshot upload');
        console.error('📸 Screenshot upload URL was:', `${this.backendUrl}/projects/${testData.projectId}/test-runs/${testData.testRunId}/test-results/${testResultId}/screenshot`);
      } else {
        console.error('📸 Screenshot upload error:', (error as Error).message);
      }
    }
  }

  public close() {
    this.server.close(() => {
      console.log('HTTP server closed');
      this.wss.close(() => {
        console.log('WebSocket server closed');
      });
    });
  }
}

// Handle graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM signal received: closing HTTP server');
  if (server) {
    server.close();
  }
  process.exit(0);
});

// Start the server
const server = new TestRunnerWebSocketServer(3024);

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('Shutting down TestRun WebSocket server...');
  server.close();
  process.exit(0);
});

export default TestRunnerWebSocketServer;
